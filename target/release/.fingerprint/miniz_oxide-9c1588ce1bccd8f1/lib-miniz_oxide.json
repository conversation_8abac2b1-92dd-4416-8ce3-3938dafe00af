{"rustc": 13226066032359371072, "features": "[\"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 5627820096486484124, "path": 13137852031270408676, "deps": [[7911289239703230891, "adler2", false, 1994407571590964478]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/miniz_oxide-9c1588ce1bccd8f1/dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}