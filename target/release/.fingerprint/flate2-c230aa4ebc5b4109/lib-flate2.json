{"rustc": 13226066032359371072, "features": "[\"any_impl\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 2040997289075261528, "path": 16195192014623565348, "deps": [[5466618496199522463, "crc32fast", false, 11612363359663224793], [7636735136738807108, "miniz_oxide", false, 13086611832913585192]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/flate2-c230aa4ebc5b4109/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}