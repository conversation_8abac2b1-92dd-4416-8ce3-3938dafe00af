use anyhow::{Context, Result};
// use quick_xml::events::{BytesEnd, BytesStart, BytesText, Event};
// use quick_xml::{Reader, Writer};
use regex::Regex;
use std::collections::HashMap;
use std::fs::File;
use std::io::{BufReader, Write};
use zip::{ZipArchive, ZipWriter};

pub struct DocxProcessor;

impl DocxProcessor {
    pub fn new() -> Self {
        Self
    }

    pub fn process_template(
        &self,
        template_path: &str,
        output_path: &str,
        replacements: &HashMap<String, String>,
    ) -> Result<()> {
        // 读取DOCX文件
        let file = File::open(template_path)
            .with_context(|| format!("无法打开模板文件: {}", template_path))?;
        let mut archive = ZipArchive::new(BufReader::new(file))?;

        // 创建输出文件
        let output_file = File::create(output_path)
            .with_context(|| format!("无法创建输出文件: {}", output_path))?;
        let mut zip_writer = ZipWriter::new(output_file);

        // 处理ZIP文件中的每个条目
        for i in 0..archive.len() {
            let mut file = archive.by_index(i)?;
            let file_name = file.name().to_string();

            // 开始写入新的ZIP条目
            zip_writer.start_file(&file_name, zip::write::FileOptions::default())?;

            if file_name == "word/document.xml" {
                // 处理主文档XML
                let mut content = String::new();
                std::io::Read::read_to_string(&mut file, &mut content)?;
                let processed_content = self.replace_placeholders(&content, replacements)?;
                zip_writer.write_all(processed_content.as_bytes())?;
            } else {
                // 直接复制其他文件
                std::io::copy(&mut file, &mut zip_writer)?;
            }
        }

        zip_writer.finish()?;
        Ok(())
    }

    fn replace_placeholders(
        &self,
        xml_content: &str,
        replacements: &HashMap<String, String>,
    ) -> Result<String> {
        // 创建正则表达式来匹配占位符
        let placeholder_regex = Regex::new(r"\{\s*([^}]+)\s*\}")?;
        
        let mut result = xml_content.to_string();
        
        // 替换所有占位符
        result = placeholder_regex.replace_all(&result, |caps: &regex::Captures| {
            let placeholder = caps.get(1).unwrap().as_str().trim();
            replacements.get(placeholder).cloned().unwrap_or_else(|| {
                // 如果没有找到替换值，保留原占位符或替换为空字符串
                String::new()
            })
        }).to_string();

        Ok(result)
    }

    pub fn extract_placeholders(&self, template_path: &str) -> Result<Vec<String>> {
        let file = File::open(template_path)?;
        let mut archive = ZipArchive::new(BufReader::new(file))?;
        
        // 查找document.xml文件
        let mut document_xml = None;
        for i in 0..archive.len() {
            let file = archive.by_index(i)?;
            if file.name() == "word/document.xml" {
                document_xml = Some(i);
                break;
            }
        }

        if let Some(index) = document_xml {
            let mut file = archive.by_index(index)?;
            let mut content = String::new();
            std::io::Read::read_to_string(&mut file, &mut content)?;
            
            let placeholder_regex = Regex::new(r"\{\s*([^}]+)\s*\}")?;
            let mut placeholders = Vec::new();
            
            for caps in placeholder_regex.captures_iter(&content) {
                let placeholder = caps.get(1).unwrap().as_str().trim().to_string();
                if !placeholders.contains(&placeholder) {
                    placeholders.push(placeholder);
                }
            }
            
            Ok(placeholders)
        } else {
            Ok(Vec::new())
        }
    }
}
